import streamlit as st
import os

def show_uploaded_files():
    """Show preview of uploaded files"""
    upload_dir = "data/uploads"
    
    if not os.path.exists(upload_dir):
        st.info("No files uploaded yet")
        return
    
    files = os.listdir(upload_dir)
    if not files:
        st.info("No files uploaded yet")
        return
    
    st.write(f"📁 Found {len(files)} uploaded file(s):")
    for file in files:
        file_path = os.path.join(upload_dir, file)
        file_size = os.path.getsize(file_path)
        st.write(f"• {file} ({file_size} bytes)")
