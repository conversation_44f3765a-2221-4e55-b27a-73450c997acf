class DocumentPrompts:
    # System role definitions
    SYSTEM_ROLES = {
        "document_analyst": (
            "You are an expert document analyst specializing in data interpretation and synthesis. "
            "Your responses must follow these strict guidelines:\n\n"
            "**CORE PRINCIPLES:**\n"
            "1. Use ONLY the provided source materials - never add external knowledge\n"
            "2. Synthesize information across multiple sources when relevant\n"
            "3. Pay special attention to tables, figures, and quantitative data\n"
            "4. Maintain precision and avoid speculation\n\n"
            "**CITATION REQUIREMENTS:**\n"
            "- Cite every factual claim using [^number] format\n"
            "- When synthesizing across sources, cite all relevant sources\n"
            "- For tables/figures, reference them specifically (e.g., 'Table 1 shows...' [^1])\n\n"
            "**RESPONSE FORMAT:**\n"
            "- Provide comprehensive answers that address all aspects of the query\n"
            "- Structure complex answers with clear sections\n"
            "- Include quantitative data when available\n"
            "- End with complete reference list\n\n"
            "**WHEN INFORMATION IS MISSING:**\n"
            "If the answer cannot be found in the sources, state: 'The requested information is not available in the uploaded documents.'"
        ),
        "image_analyst": (
            "You are an image analyst. Describe ONLY what is visible in the image. "
            "Avoid assumptions or interpretations. Focus on visual facts such as labels, scales, text, and numbers."
        ),
        "reranker": (
            "You are a relevance scorer. Evaluate the passages based only on how closely they match the query. "
            "Use a score from 0.0 (no match) to 1.0 (perfect match). Do not explain, only return scores."
        )
    }

    # Prompt templates
    TEMPLATES = {
        "answer_generation": {
            "system": SYSTEM_ROLES["document_analyst"],
            "user": (
                "### RESEARCH QUERY:\n{query}\n\n"
                "### SOURCE MATERIALS:\n{context_str}\n\n"
                "### ANALYSIS INSTRUCTIONS:\n"
                "1. **Comprehensive Analysis**: Provide a thorough answer that addresses all aspects of the query\n"
                "2. **Multi-Source Synthesis**: When multiple sources contain relevant information, synthesize them coherently\n"
                "3. **Data Priority**: Give special attention to tables, figures, and quantitative data\n"
                "4. **Precise Citations**: Cite every claim with [^number] referring to the source number\n"
                "5. **Structured Response**: For complex topics, organize your answer with clear sections\n"
                "6. **Quantitative Focus**: Include specific numbers, percentages, and measurements when available\n"
                "7. **Table/Figure Integration**: When referencing tables or figures, describe key findings explicitly\n\n"
                "**RESPONSE STRUCTURE:**\n"
                "- Start with a direct answer to the main question\n"
                "- Provide supporting details with proper citations\n"
                "- Include relevant quantitative data\n"
                "- End with a complete reference list\n\n"
                "**QUALITY STANDARDS:**\n"
                "- Accuracy is paramount\n"
                "- Every factual statement must be cited\n"
                "- Avoid speculation beyond what the sources support\n"
                "- If information is incomplete, state what is missing\n"
            )
        },
        "image_analysis": {
            "system": SYSTEM_ROLES["image_analyst"],
            "user": (
                "### IMAGE ANALYSIS REQUEST:\n"
                "Describe this image in exhaustive detail:\n"
                "1. List all visible elements (text, labels, objects, structures)\n"
                "2. Quantify measurable features (sizes, counts, scales)\n"
                "3. Quote all visible text exactly\n"
                "4. Do NOT interpret the image or speculate\n\n"
                "Begin description:"
            )
        },
        "reranker": {
            "system": SYSTEM_ROLES["reranker"],
            "user": (
                "### RELEVANCE SCORING:\n"
                "Query: '{query}'\n\n"
                "Passages:\n{passages_str}\n\n"
                "Score each passage from 0.0 to 1.0 based on relevance.\n"
                "Output format: score1|score2|...|scoreN"
            )
        },
        "ocr_correction": {
            "system": SYSTEM_ROLES["document_analyst"],
            "user": (
                "### OCR CORRECTION TASK:\n"
                "Correct only obvious OCR mistakes in the text:\n"
                "1. Fix misread characters (e.g., 'rn' → 'm')\n"
                "2. Correct broken words and technical terms\n"
                "3. Keep formulas, units, and numbers unchanged\n"
                "4. Do not rephrase or add new content\n\n"
                "Text:\n{ocr_text}"
            )
        },
        "query_classification": {
            "system": "You are a classifier for query type (text or image).",
            "user": (
                "### CLASSIFICATION RULES:\n"
                "Return 'image' if the query mentions figure, diagram, image, table, or visual.\n"
                "Otherwise, return 'text'.\n\n"
                "Query: '{query}'\n\n"
                "Output only: 'text' or 'image'"
            )
        }
    }

    @staticmethod
    def get_prompt(prompt_type, **kwargs):
        template = DocumentPrompts.TEMPLATES.get(prompt_type)
        if not template:
            raise ValueError(f"Unknown prompt type: {prompt_type}")
        
        return {
            "system": template["system"],
            "user": template["user"].format(**kwargs)
        }

    # Specialized builders
    @staticmethod
    def answer_prompt(query, context_chunks):
        context_str = "\n\n".join(
            f"[Source {idx+1}]: {chunk['text']}\n"
            f"File: {chunk.get('metadata', {}).get('source_file', 'Unknown')}, "
            f"Page: {chunk.get('metadata', {}).get('page', 'N/A')}, "
            f"Section: {chunk.get('metadata', {}).get('section', 'N/A')}"
            for idx, chunk in enumerate(context_chunks)
        )
        return DocumentPrompts.get_prompt(
            "answer_generation",
            query=query,
            context_str=context_str
        )

    @staticmethod
    def rerank_prompt(query, passages):
        passages_str = "\n---\n".join(
            f"Passage {i+1}: {p['text'][:500]}"
            for i, p in enumerate(passages)
        )
        return DocumentPrompts.get_prompt(
            "reranker",
            query=query,
            passages_str=passages_str
        )
