# Document RAG System - Installation Guide

## Quick Setup

### 1. Install Missing Dependencies
```bash
# Install OpenAI and NLTK (the missing packages)
pip install openai nltk

# Or install all requirements
pip install -r requirements.txt
```

### 2. Setup NLTK Data
```bash
python -c "import nltk; nltk.download('punkt')"
```

### 3. Configure Environment
Create `.env` file:
```
OPENAI_API_KEY=your_api_key_here
```

### 4. Run Application
```bash
streamlit run app/main.py
```

## Test Table Processing
Upload PDFs with tables and try queries like:
- "What are the results shown in Table 1?"
- "Show me the data from the tables"
- "What are the values in the demographics table?"

## System Features
✅ **PDF Table Extraction** - Advanced table detection and processing  
✅ **LLM-Based Reranking** - Semantic relevance scoring  
✅ **Dynamic Chunking** - Content-aware processing with overlap  
✅ **Expanded Context** - Up to 14K tokens for comprehensive answers  
✅ **Multi-Source Synthesis** - Combines information across documents  
✅ **Precise Citations** - Every claim properly referenced  

Your system is now ready for production use!
