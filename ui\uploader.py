import streamlit as st

def upload_files():
    """File upload interface"""
    uploaded_files = st.file_uploader(
        "Choose files to upload",
        type=['pdf', 'txt', 'docx', 'csv', 'png', 'jpg', 'jpeg'],
        accept_multiple_files=True,
        help="Upload PDF, text, Word documents, CSV files, or images"
    )
    
    if uploaded_files:
        st.success(f"✅ {len(uploaded_files)} file(s) selected")
        for file in uploaded_files:
            st.write(f"📄 {file.name} ({file.size} bytes)")
    
    return uploaded_files
