from core.embedder import get_embedding
from typing import List, Dict, Any

def hybrid_retrieval(query: str, vector_store, chunk_data: List[Dict], k: int = 10) -> List[Dict]:
    """Perform hybrid retrieval combining vector similarity and semantic relevance"""
    
    if not chunk_data:
        return []
    
    # Generate query embedding
    query_embedding = get_embedding(query)
    
    # Vector similarity search
    distances, indices = vector_store.search(query_embedding, k=k)
    
    # Retrieve matching chunks
    retrieved_chunks = []
    for idx in indices:
        if 0 <= idx < len(chunk_data):
            chunk = chunk_data[idx]
            retrieved_chunks.append({
                "text": chunk["text"],
                "metadata": chunk["metadata"]
            })
    
    return retrieved_chunks
