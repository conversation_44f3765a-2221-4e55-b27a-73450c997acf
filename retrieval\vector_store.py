import faiss
import numpy as np
import os

class VectorStore:
    def __init__(self, dimension: int = 1536):
        self.dimension = dimension
        self.index = faiss.IndexFlatIP(dimension)
        self._load_existing()

    def _load_existing(self):
        """Load existing index if available"""
        index_path = "data/vector_index/index.faiss"
        if os.path.exists(index_path):
            try:
                self.index = faiss.read_index(index_path)
            except Exception:
                self.index = faiss.IndexFlatIP(self.dimension)

    def add_vectors(self, vectors: list):
        """Add vectors to the index"""
        if not vectors:
            return
        vectors_array = np.array(vectors, dtype=np.float32)
        faiss.normalize_L2(vectors_array)
        self.index.add(vectors_array)
        self.save()

    def search(self, query_vector, k=10):
        """Search for top-k similar vectors"""
        if self.index.ntotal == 0:
            return [], []
        query_array = np.array([query_vector], dtype=np.float32)
        faiss.normalize_L2(query_array)
        distances, indices = self.index.search(query_array, k)
        return distances[0], indices[0]

    def save(self):
        """Save index to disk"""
        index_path = os.path.join("data", "vector_index", "index.faiss")
        os.makedirs(os.path.dirname(index_path), exist_ok=True)
        faiss.write_index(self.index, index_path)
