import os
import sys
import streamlit as st
from dotenv import load_dotenv

# Setup paths
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
load_dotenv()

# UI modules
from ui.uploader import upload_files
from ui.chatbox import query_input
from ui.preview import show_uploaded_files

# Core pipeline
from core.ingestion import Ingestion
from retrieval.hybrid_search import hybrid_retrieval
from retrieval.reranker import batch_rerank
from llm.response_generator import generate_cited_response

st.set_page_config(page_title="LIFESAVER-AI", layout="wide")
st.title("📄 LIFESAVER-AI")

# Initialize session state
if 'ingestion_system' not in st.session_state:
    st.session_state.ingestion_system = Ingestion()
if 'ingestion_complete' not in st.session_state:
    st.session_state.ingestion_complete = False

# Get ingestion system
ingest = st.session_state.ingestion_system

# Upload files
with st.expander("📤 Upload Files", expanded=True):
    files = upload_files()

# Show current status
processed_files = ingest.metadata_store.get_processed_files()
if processed_files:
    st.info(f"📁 Already processed files: {', '.join(processed_files)}")
    chunk_count = len(ingest.metadata_store.chunks)
    vector_count = ingest.vector_store.index.ntotal
    st.info(f"📊 Current index: {chunk_count} chunks, {vector_count} vectors")

# Ingest documents
if st.button("🚀 Ingest Files") and files:
    with st.spinner("Processing new files..."):
        try:
            new_files_processed = 0
            skipped_files = 0
            
            # Process each file
            for file in files:
                filename = file.name
                
                # Check if already processed
                if ingest.metadata_store.is_file_processed(filename):
                    skipped_files += 1
                    continue
                
                file_path = f"data/uploads/{filename}"
                os.makedirs("data/uploads", exist_ok=True)
                with open(file_path, "wb") as f:
                    f.write(file.getbuffer())
                
                ingest.ingest_file(file_path)
                new_files_processed += 1
            
            if new_files_processed > 0:
                st.session_state.ingestion_complete = True
                st.success(f"✅ Processed {new_files_processed} new files!")
            
            if skipped_files > 0:
                st.info(f"⏭️ Skipped {skipped_files} already processed files")

            # Show final status
            chunk_count = len(ingest.metadata_store.chunks)
            vector_count = ingest.vector_store.index.ntotal
            st.info(f"📊 Total index: {chunk_count} chunks, {vector_count} vectors")
            
        except Exception as e:
            st.error(f"❌ Ingestion failed: {str(e)}")

# Preview uploaded files
with st.expander("📄 Uploaded Files Preview"):
    show_uploaded_files()

# Query interface - Allow querying if there are any processed files
if len(ingest.metadata_store.chunks) > 0:
    if not st.session_state.ingestion_complete and processed_files:
        st.info("💡 Using existing processed files for querying")
    
    query = query_input()
    
    if query:
        with st.spinner("🔍 Searching and generating response..."):
            try:
                chunk_data = ingest.metadata_store.get_all_chunks()
                
                if not chunk_data:
                    st.warning("No indexed content found. Please ingest files first.")
                else:
                    # Perform retrieval with expanded search
                    retrieved_chunks = hybrid_retrieval(
                        query=query,
                        vector_store=ingest.vector_store,
                        chunk_data=chunk_data,
                        k=20
                    )

                    # Enhanced reranking and response generation
                    if retrieved_chunks:
                        # Rerank with LLM cross-encoder
                        top_passages = batch_rerank(query, retrieved_chunks, top_k=10)
                        best_chunks = [x["passage"] for x in top_passages]

                        # Generate response with expanded context
                        response = generate_cited_response(query, best_chunks)

                        # Display response with enhanced formatting
                        st.markdown("### 📊 Results")
                        st.markdown(response)

                        # Show retrieval details in expander
                        with st.expander("📊 Retrieval Details"):
                            st.write(f"**Retrieved:** {len(retrieved_chunks)} chunks")
                            st.write(f"**Reranked:** {len(best_chunks)} chunks used for response")

                            # Show content type distribution
                            content_types = {}
                            for chunk in best_chunks:
                                ctype = chunk.get("metadata", {}).get("content_type", "text")
                                content_types[ctype] = content_types.get(ctype, 0) + 1

                            if content_types:
                                st.write("**Content Types Used:**")
                                for ctype, count in content_types.items():
                                    st.write(f"- {ctype.title()}: {count} chunk{'s' if count > 1 else ''}")

                        # Show source chunks in another expander
                        with st.expander("📄 Source Chunks"):
                            for i, chunk in enumerate(best_chunks[:5]):
                                metadata = chunk.get("metadata", {})
                                st.write(f"**Chunk {i+1}** ({metadata.get('content_type', 'text')})")
                                st.write(f"Source: {metadata.get('source_file', 'Unknown')}, Page: {metadata.get('page', 'N/A')}")
                                st.write(f"Section: {metadata.get('section', 'N/A')}")
                                st.text(chunk["text"][:300] + "..." if len(chunk["text"]) > 300 else chunk["text"])
                                st.write("---")
                    else:
                        st.warning("No relevant content found for your query.")
                    
            except Exception as e:
                st.error(f"Query processing failed: {str(e)}")
else:
    st.info("👆 Please upload and ingest files first to start querying.")
